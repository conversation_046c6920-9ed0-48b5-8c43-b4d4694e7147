import React, { useState, useEffect, useRef } from 'react';
import { 
  Home, Grid, BarChart3, Upload, Download, Play,
  CheckCircle, AlertCircle, Clock, Cpu, Zap, FolderOpen,
  X, FileText, RefreshCw, Activity, Eye, Layers,
  Timer, HardDrive, TrendingUp, Package, Folder,
  Settings, Sparkles, ChevronRight
} from 'lucide-react';

// 动画数字组件
const AnimatedNumber = ({ value, duration = 1000 }) => {
  const [displayValue, setDisplayValue] = useState(0);
  
  useEffect(() => {
    let startTime;
    const startValue = displayValue;
    const endValue = parseFloat(value) || 0;
    
    const animate = (currentTime) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      const easeOut = 1 - Math.pow(1 - progress, 3);
      const current = startValue + (endValue - startValue) * easeOut;
      
      setDisplayValue(current);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }, [value, duration]);
  
  return <>{displayValue.toFixed(1)}</>;
};

// WebSocket Hook
const useWebSocket = (url) => {
  const [progress, setProgress] = useState({
    percentage: 0,
    stage: '',
    message: '',
    speed: '',
    eta: '',
    currentFile: '',
    processedFiles: 0,
    totalFiles: 0
  });
  
  useEffect(() => {
    // 模拟WebSocket数据
    const interval = setInterval(() => {
      setProgress(prev => ({
        ...prev,
        percentage: Math.min(prev.percentage + Math.random() * 5, 100),
        speed: `${(25 + Math.random() * 10).toFixed(1)} MB/s`
      }));
    }, 500);
    
    return () => clearInterval(interval);
  }, [url]);
  
  return progress;
};

// 文件上传组件
const FileDropzone = ({ onFilesSelected, multiple = false, acceptFolder = false, folderOnly = false }) => {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);
  const folderInputRef = useRef(null);

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    const items = Array.from(e.dataTransfer.items);
    const files = [];
    
    // 处理拖拽的文件
    items.forEach(item => {
      if (item.kind === 'file') {
        const file = item.getAsFile();
        if (file && (file.name.endsWith('.las') || file.name.endsWith('.laz'))) {
          files.push(file);
        }
      }
    });
    
    if (files.length > 0) {
      onFilesSelected(files);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleFolderClick = () => {
    folderInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files).filter(
      file => file.name.endsWith('.las') || file.name.endsWith('.laz')
    );
    if (files.length > 0) {
      onFilesSelected(files);
    }
  };

  return (
    <div
      className={`relative border-2 border-dashed rounded-2xl p-6 text-center transition-all duration-300 overflow-hidden ${
        isDragging 
          ? 'border-cyan-400 bg-cyan-400/10 scale-105 shadow-2xl shadow-cyan-500/20' 
          : 'border-gray-600 hover:border-gray-500 bg-gray-800/30 hover:bg-gray-800/40'
      }`}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        multiple={multiple}
        accept=".las,.laz"
        onChange={handleFileChange}
      />
      <input
        ref={folderInputRef}
        type="file"
        className="hidden"
        webkitdirectory=""
        directory=""
        multiple
        onChange={handleFileChange}
      />
      
      {/* 背景网格效果 */}
      <div className="absolute inset-0 opacity-5">
        <div 
          className="h-full w-full"
          style={{
            backgroundImage: `
              linear-gradient(cyan 1px, transparent 1px),
              linear-gradient(90deg, cyan 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px',
            backgroundPosition: '-1px -1px'
          }}
        />
      </div>
      
      {/* 扫描线效果 */}
      {isDragging && (
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent animate-slide-down" />
        </div>
      )}
      
      <div className="relative z-10">
        <div className={`w-12 h-12 mx-auto mb-3 transition-all duration-500 ${isDragging ? 'scale-125 rotate-12' : ''}`}>
          <Upload className={`w-full h-full ${isDragging ? 'text-cyan-400' : 'text-gray-400'}`} />
        </div>
        <p className="text-sm font-bold text-gray-200 mb-3">
          {folderOnly ? '拖拽文件夹到此处' : '拖拽文件到此处'}
        </p>
        <div className="flex gap-2 justify-center">
          {!folderOnly && (
            <button
              onClick={handleFileClick}
              className="px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 backdrop-blur rounded-lg text-xs font-semibold text-gray-200 transition-all hover:scale-105 border border-gray-600"
            >
              选择文件
            </button>
          )}
          {(acceptFolder || folderOnly) && (
            <button
              onClick={handleFolderClick}
              className="px-4 py-2 bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-500 hover:to-blue-500 rounded-lg text-xs font-semibold text-white transition-all hover:scale-105 shadow-lg shadow-cyan-500/25"
            >
              选择文件夹
            </button>
          )}
        </div>
        <p className="text-xs text-gray-500 mt-2">
          {folderOnly ? '选择包含 .las/.laz 文件的文件夹' : '支持 .las, .laz 格式'}
        </p>
      </div>
    </div>
  );
};

// 修复后的滑块组件
const SliderControl = ({ label, value, onChange, min, max, step = 1, unit = '', hint = '' }) => {
  const percentage = ((value - min) / (max - min)) * 100;
  
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-sm font-bold text-gray-200">{label}</label>
        <span className="text-sm font-mono font-bold text-cyan-400">
          {value}{unit}
        </span>
      </div>
      
      <div className="relative">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-thumb"
          style={{
            background: `linear-gradient(to right, #00d4ff 0%, #00d4ff ${percentage}%, #374151 ${percentage}%, #374151 100%)`
          }}
        />
      </div>
      
      {hint && (
        <p className="text-xs text-gray-500 italic">{hint}</p>
      )}
    </div>
  );
};

// 卡片组件
const Card = ({ title, children, className = '', icon: Icon, gradient = false }) => (
  <div className={`relative bg-gray-800/50 backdrop-blur-xl rounded-2xl p-6 border border-gray-700/50 overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-cyan-500/10 ${className}`}>
    {gradient && (
      <>
        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-5">
          <div 
            className="h-full w-full"
            style={{
              backgroundImage: `
                linear-gradient(cyan 1px, transparent 1px),
                linear-gradient(90deg, cyan 1px, transparent 1px)
              `,
              backgroundSize: '30px 30px',
              backgroundPosition: '-1px -1px'
            }}
          />
        </div>
        {/* 渐变边框效果 */}
        <div className="absolute top-0 right-0 w-64 h-0.5 bg-gradient-to-r from-transparent via-cyan-500/50 to-transparent" />
        <div className="absolute bottom-0 left-0 w-64 h-0.5 bg-gradient-to-r from-transparent via-blue-500/50 to-transparent" />
      </>
    )}
    {title && (
      <div className="flex items-center gap-3 mb-4 relative z-10">
        {Icon && (
          <div className="w-10 h-10 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-lg flex items-center justify-center border border-cyan-500/30">
            <Icon className="w-5 h-5 text-cyan-400" />
          </div>
        )}
        <h3 className="text-lg font-bold text-gray-100">{title}</h3>
      </div>
    )}
    <div className="relative z-10">
      {children}
    </div>
  </div>
);

// 进度条组件
const ProgressBar = ({ percentage, showLabel = true }) => (
  <div className="w-full">
    {showLabel && (
      <div className="flex justify-between text-sm mb-2">
        <span className="text-gray-400 font-medium">进度</span>
        <span className="text-cyan-400 font-mono font-bold">{percentage.toFixed(1)}%</span>
      </div>
    )}
    <div className="relative">
      <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 blur-sm rounded-full" />
      <div className="relative w-full bg-gray-700/50 rounded-full h-3 overflow-hidden backdrop-blur">
        <div
          className="h-full rounded-full transition-all duration-700 ease-out relative overflow-hidden"
          style={{ 
            width: `${percentage}%`,
            background: 'linear-gradient(90deg, #00d4ff 0%, #0066ff 50%, #00d4ff 100%)',
            backgroundSize: '200% 100%',
            animation: 'gradient 3s ease infinite'
          }}
        >
          <div className="absolute inset-0 bg-white/20 animate-pulse" />
          <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-r from-transparent to-white/30 blur-sm" />
        </div>
      </div>
    </div>
  </div>
);

// 统计卡片组件
const StatCard = ({ icon: Icon, label, value, color = 'cyan' }) => {
  const colorClasses = {
    cyan: 'from-cyan-500 to-blue-500',
    green: 'from-green-500 to-emerald-500',
    purple: 'from-purple-500 to-pink-500',
    orange: 'from-orange-500 to-red-500'
  };

  return (
    <div className="relative bg-gray-700/30 backdrop-blur rounded-xl p-4 border border-gray-700/50 overflow-hidden group hover:scale-105 transition-all duration-300">
      {/* 网格背景 */}
      <div className="absolute inset-0 opacity-5">
        <div 
          className="h-full w-full"
          style={{
            backgroundImage: `linear-gradient(${color === 'cyan' ? 'cyan' : color === 'green' ? '#10b981' : color === 'purple' ? '#a855f7' : '#f97316'} 1px, transparent 1px)`,
            backgroundSize: '15px 15px'
          }}
        />
      </div>
      
      {/* 边框光效 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-current to-transparent opacity-50" />
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-current to-transparent opacity-50" />
      </div>
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-2">
          <div className={`w-10 h-10 bg-gradient-to-br ${colorClasses[color]} rounded-lg flex items-center justify-center shadow-lg`}>
            <Icon className="w-5 h-5 text-white" />
          </div>
          <span className={`text-2xl font-bold bg-gradient-to-r ${colorClasses[color]} bg-clip-text text-transparent`}>
            {value}
          </span>
        </div>
        <p className="text-sm text-gray-400 font-medium">{label}</p>
      </div>
    </div>
  );
};

// 图像查看器组件
const ImageViewer = ({ images, selectedImage, onSelectImage }) => {
  const imageColors = {
    '原始点云': 'border-blue-500',
    '圆柱拟合': 'border-purple-500',
    '地面缺口检测': 'border-green-500',
    '隧道展开图': 'border-orange-500',
    '地面详图': 'border-pink-500'
  };

  return (
    <div className="space-y-4">
      {/* 主显示区域 */}
      <div className="relative bg-gray-900 rounded-xl overflow-hidden h-[400px] group">
        {selectedImage ? (
          <>
            <img
              src={selectedImage.url}
              alt={selectedImage.name}
              className="w-full h-full object-contain"
            />
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <button className="bg-gray-800/80 backdrop-blur p-2 rounded-lg hover:bg-gray-700/80">
                <Download className="w-5 h-5" />
              </button>
            </div>
            <div className="absolute bottom-4 left-4 bg-gray-800/80 backdrop-blur px-4 py-2 rounded-lg">
              <p className="text-sm font-semibold text-gray-200">{selectedImage.name}</p>
              <p className="text-xs text-gray-400">点击下方缩略图切换查看</p>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Eye className="w-12 h-12 text-gray-600 mx-auto mb-3" />
              <p className="text-gray-400">等待处理结果...</p>
            </div>
          </div>
        )}
      </div>

      {/* 缩略图网格 */}
      <div className="grid grid-cols-5 gap-3">
        {images.map((image, index) => (
          <div
            key={index}
            onClick={() => onSelectImage(image)}
            className={`relative rounded-lg overflow-hidden cursor-pointer transition-all duration-300 border-2 ${
              selectedImage?.name === image.name 
                ? imageColors[image.name] 
                : 'border-gray-700 hover:border-gray-600'
            }`}
          >
            <img
              src={image.url}
              alt={image.name}
              className="w-full h-20 object-cover"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-gray-900 to-transparent p-2">
              <p className="text-xs text-gray-300 truncate font-medium">{image.name}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// 特征柱状图组件
const FeatureBarChart = ({ features }) => {
  const maxValue = Math.max(...Object.values(features));
  const colors = {
    spatial: 'from-blue-500 to-cyan-500',
    color: 'from-purple-500 to-pink-500',
    density: 'from-green-500 to-emerald-500',
    geometric: 'from-orange-500 to-red-500',
    texture: 'from-pink-500 to-rose-500'
  };
  
  const labels = {
    spatial: '空间特征',
    color: '颜色特征',
    density: '密度特征',
    geometric: '几何特征',
    texture: '纹理特征'
  };

  return (
    <div className="space-y-3">
      {Object.entries(features).map(([key, value]) => (
        <div key={key} className="space-y-1">
          <div className="flex justify-between text-sm">
            <span className="font-medium text-gray-300">{labels[key]}</span>
            <span className="font-bold text-cyan-400">{value}维</span>
          </div>
          <div className="h-8 bg-gray-700/50 rounded-lg overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r ${colors[key]} transition-all duration-1000`}
              style={{ width: `${(value / maxValue) * 100}%` }}
            />
          </div>
        </div>
      ))}
    </div>
  );
};

// 单图像隧道展开页面
const SingleTunnel = () => {
  const [file, setFile] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  
  const [params, setParams] = useState({
    sampleRatio: 10,
    method: 'pca',
    nBins: 72,
    windowSize: 12,
    deviationThreshold: 0.5,
    resolution: 100
  });

  const progress = useWebSocket('ws://localhost:8000/api/ws');

  const handleFileSelect = (files) => {
    setFile(files[0]);
    setResults(null);
  };

  const handleProcess = async () => {
    if (!file) return;
    
    setIsProcessing(true);
    
    setTimeout(() => {
      const images = [
        { name: '原始点云', url: '/api/placeholder/800/600' },
        { name: '圆柱拟合', url: '/api/placeholder/800/600' },
        { name: '地面缺口检测', url: '/api/placeholder/800/600' },
        { name: '隧道展开图', url: '/api/placeholder/800/600' },
        { name: '地面详图', url: '/api/placeholder/800/600' }
      ];
      
      setResults({
        images,
        stats: {
          processTime: 15.3,
          processSpeed: 28.5,
          pointCount: 1234567,
          fileSize: 436.2
        }
      });
      setSelectedImage(images[3]); // 默认显示隧道展开图
      setIsProcessing(false);
    }, 3000);
  };

  return (
    <div className="space-y-6">
      {/* 第一行：文件上传和参数设置 */}
      <div className="grid grid-cols-2 gap-6">
        {/* 文件上传 */}
        <Card title="文件上传" icon={Upload} gradient>
          {!file ? (
            <FileDropzone onFilesSelected={handleFileSelect} />
          ) : (
            <div className="bg-gradient-to-r from-gray-700/30 to-gray-800/30 rounded-xl p-4 flex items-center justify-between group hover:from-gray-700/40 hover:to-gray-800/40 transition-all duration-300">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-cyan-500/25">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <p className="font-semibold text-gray-200">{file.name}</p>
                  <p className="text-sm text-gray-400">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <button
                onClick={() => setFile(null)}
                className="text-gray-400 hover:text-red-400 transition-colors transform hover:scale-110"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          )}
        </Card>

        {/* 参数配置 */}
        <Card title="参数配置" icon={Settings} gradient>
          <div className="space-y-4">
            <div className="p-4 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg border border-cyan-500/20">
              <div className="grid grid-cols-2 gap-4">
                <SliderControl
                  label="采样率"
                  value={params.sampleRatio}
                  onChange={(v) => setParams({...params, sampleRatio: v})}
                  min={1}
                  max={100}
                  unit="%"
                />
                
                <SliderControl
                  label="输出分辨率"
                  value={params.resolution}
                  onChange={(v) => setParams({...params, resolution: v})}
                  min={50}
                  max={200}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-bold text-gray-200 mb-2 block">圆柱拟合方法</label>
                <select
                  value={params.method}
                  onChange={(e) => setParams({...params, method: e.target.value})}
                  className="w-full bg-gray-700/50 backdrop-blur rounded-lg px-3 py-2 text-gray-200 text-sm border border-gray-600 focus:border-cyan-500 focus:outline-none transition-colors"
                >
                  <option value="pca">PCA主成分分析</option>
                  <option value="ransac">RANSAC随机采样</option>
                </select>
              </div>

              <SliderControl
                label="偏差阈值"
                value={params.deviationThreshold}
                onChange={(v) => setParams({...params, deviationThreshold: v})}
                min={0.1}
                max={1.0}
                step={0.1}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <SliderControl
                label="角度分区数"
                value={params.nBins}
                onChange={(v) => setParams({...params, nBins: v})}
                min={36}
                max={144}
              />

              <SliderControl
                label="滑动窗口"
                value={params.windowSize}
                onChange={(v) => setParams({...params, windowSize: v})}
                min={6}
                max={24}
              />
            </div>

            <button
              onClick={handleProcess}
              disabled={!file || isProcessing}
              className={`w-full py-3 rounded-xl font-bold transition-all flex items-center justify-center gap-3 shadow-lg ${
                !file || isProcessing
                  ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white hover:from-cyan-600 hover:to-blue-600 hover:shadow-cyan-500/25 transform hover:scale-105'
              }`}
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="w-5 h-5 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5" />
                  开始处理
                </>
              )}
            </button>
          </div>
        </Card>
      </div>

      {/* 处理进度 */}
      {isProcessing && (
        <Card title="处理进度" icon={Activity} gradient>
          <div className="space-y-4">
            <ProgressBar percentage={progress.percentage || 45} />
            
            <div className="bg-gradient-to-r from-gray-700/30 to-gray-800/30 rounded-xl p-3 border border-gray-700/50">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
                <p className="text-sm text-gray-300">
                  状态：<span className="text-cyan-400 font-semibold">{progress.message || "正在进行圆柱拟合..."}</span>
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-4">
              <StatCard icon={Zap} label="处理速度" value={progress.speed || "25.6 MB/s"} color="cyan" />
              <StatCard icon={Clock} label="已用时间" value="12.5秒" color="green" />
              <StatCard icon={Layers} label="处理点数" value="856K" color="purple" />
              <StatCard icon={Timer} label="预计剩余" value="15秒" color="orange" />
            </div>
          </div>
        </Card>
      )}

      {/* 处理结果 */}
      {results && (
        <>
          <Card title="处理结果 - 5张输出图像" icon={Eye} gradient>
            <ImageViewer
              images={results.images}
              selectedImage={selectedImage}
              onSelectImage={setSelectedImage}
            />
            
            <div className="mt-4 flex gap-4">
              <button className="flex-1 bg-gray-700/50 hover:bg-gray-600/50 text-gray-200 py-3 px-4 rounded-xl font-semibold transition-all hover:scale-105 flex items-center justify-center gap-2 shadow-lg">
                <Download className="w-4 h-4" />
                下载当前图像
              </button>
              <button className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white py-3 px-4 rounded-xl font-semibold transition-all transform hover:scale-105 hover:shadow-cyan-500/25 flex items-center justify-center gap-2 shadow-lg">
                <Download className="w-4 h-4" />
                下载全部结果
              </button>
            </div>
          </Card>

          <Card title="处理统计" icon={BarChart3} gradient>
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-gradient-to-br from-gray-700/30 to-gray-800/30 rounded-xl p-4 text-center border border-gray-700/50 hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <Timer className="w-6 h-6 text-cyan-400" />
                </div>
                <p className="text-2xl font-bold text-cyan-400">{results.stats.processTime}秒</p>
                <p className="text-sm text-gray-400 font-medium">处理时间</p>
              </div>
              <div className="bg-gradient-to-br from-gray-700/30 to-gray-800/30 rounded-xl p-4 text-center border border-gray-700/50 hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="w-6 h-6 text-green-400" />
                </div>
                <p className="text-2xl font-bold text-green-400">{results.stats.processSpeed} MB/s</p>
                <p className="text-sm text-gray-400 font-medium">处理速度</p>
              </div>
              <div className="bg-gradient-to-br from-gray-700/30 to-gray-800/30 rounded-xl p-4 text-center border border-gray-700/50 hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <Layers className="w-6 h-6 text-purple-400" />
                </div>
                <p className="text-2xl font-bold text-purple-400">{results.stats.pointCount.toLocaleString()}</p>
                <p className="text-sm text-gray-400 font-medium">处理点数</p>
              </div>
              <div className="bg-gradient-to-br from-gray-700/30 to-gray-800/30 rounded-xl p-4 text-center border border-gray-700/50 hover:scale-105 transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <HardDrive className="w-6 h-6 text-orange-400" />
                </div>
                <p className="text-2xl font-bold text-orange-400">{results.stats.fileSize} MB</p>
                <p className="text-sm text-gray-400 font-medium">文件大小</p>
              </div>
            </div>
          </Card>
        </>
      )}
    </div>
  );
};

// 批量处理页面
const BatchProcess = () => {
  const [files, setFiles] = useState([]);
  const [folderName, setFolderName] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedFileId, setSelectedFileId] = useState(null);
  const [selectedFileResults, setSelectedFileResults] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [batchParams, setBatchParams] = useState({
    numWorkers: 3,
    skipExisting: true,
    sampleRatio: 10,
    method: 'pca',
    nBins: 72,
    windowSize: 12,
    deviationThreshold: 0.5,
    resolution: 100
  });

  const handleFilesSelect = (newFiles) => {
    // 过滤出.las和.laz文件
    const lasFiles = newFiles.filter(file => 
      file.name.endsWith('.las') || file.name.endsWith('.laz')
    );
    
    if (lasFiles.length > 0) {
      // 尝试获取文件夹名称
      if (lasFiles[0].webkitRelativePath) {
        const pathParts = lasFiles[0].webkitRelativePath.split('/');
        if (pathParts.length > 1) {
          setFolderName(pathParts[0]);
        }
      }
      
      const fileList = lasFiles.map((file, index) => ({
        id: Date.now() + index,
        name: file.name,
        size: (file.size / 1024 / 1024).toFixed(2),
        status: 'waiting',
        progress: 0,
        file,
        results: null
      }));
      setFiles(fileList);
    }
  };

  const handleBatchProcess = () => {
    if (files.length === 0) return;
    setIsProcessing(true);
    
    files.forEach((file, index) => {
      setTimeout(() => {
        setFiles(prev => prev.map(f => 
          f.id === file.id 
            ? {...f, status: 'processing', progress: 0}
            : f
        ));
        
        const interval = setInterval(() => {
          setFiles(prev => prev.map(f => {
            if (f.id === file.id && f.progress < 100) {
              const newProgress = Math.min(f.progress + 10, 100);
              
              // 处理完成时生成结果
              if (newProgress === 100) {
                return {
                  ...f,
                  progress: newProgress,
                  status: 'completed',
                  results: {
                    images: [
                      { name: '原始点云', url: '/api/placeholder/800/600' },
                      { name: '圆柱拟合', url: '/api/placeholder/800/600' },
                      { name: '地面缺口检测', url: '/api/placeholder/800/600' },
                      { name: '隧道展开图', url: '/api/placeholder/800/600' },
                      { name: '地面详图', url: '/api/placeholder/800/600' }
                    ],
                    processTime: (10 + Math.random() * 10).toFixed(1),
                    processSpeed: (25 + Math.random() * 10).toFixed(1)
                  }
                };
              }
              
              return {
                ...f,
                progress: newProgress,
                status: 'processing'
              };
            }
            return f;
          }));
        }, 500);
        
        setTimeout(() => clearInterval(interval), 5000);
      }, index * 1000);
    });
  };

  const handleSelectFile = (fileId) => {
    const file = files.find(f => f.id === fileId);
    if (file && file.results) {
      setSelectedFileId(fileId);
      setSelectedFileResults(file.results);
      setSelectedImage(file.results.images[3]); // 默认显示隧道展开图
    }
  };

  const getStatusIcon = (status) => {
    switch(status) {
      case 'waiting':
        return <Clock className="w-4 h-4 text-gray-400" />;
      case 'processing':
        return <RefreshCw className="w-4 h-4 text-cyan-400 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      default:
        return null;
    }
  };

  const completedCount = files.filter(f => f.status === 'completed').length;
  const processingCount = files.filter(f => f.status === 'processing').length;
  const failedCount = files.filter(f => f.status === 'failed').length;
  const totalProgress = files.length > 0 ? (completedCount / files.length) * 100 : 0;
  
  // 计算预计完成时间
  const estimatedTime = files.length > 0 ? Math.ceil((files.length - completedCount) * 5 / batchParams.numWorkers) : 0;

  return (
    <div className="space-y-6">
      {/* 第一行：文件夹上传和参数设置 */}
      <div className="grid grid-cols-2 gap-6">
        {/* 文件夹上传 */}
        <Card title="批量文件夹上传" icon={FolderOpen} gradient>
          {files.length === 0 ? (
            <div className="space-y-4">
              <FileDropzone 
                onFilesSelected={handleFilesSelect} 
                multiple={true}
                folderOnly={true} 
              />
              <div className="text-center text-sm text-gray-400">
                <p className="flex items-center justify-center gap-2">
                  <Sparkles className="w-4 h-4 text-cyan-400" />
                  请选择包含 .las/.laz 文件的文件夹
                </p>
                <p className="text-xs mt-1">将自动扫描文件夹中的所有点云文件</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-gradient-to-r from-gray-700/30 to-gray-800/30 rounded-xl p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-cyan-500/25">
                      <FolderOpen className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-200">
                        {folderName || '已选择文件夹'}
                      </p>
                      <p className="text-sm text-gray-400">
                        {files.length} 个LAS/LAZ文件
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setFiles([]);
                      setFolderName('');
                      setSelectedFileId(null);
                      setSelectedFileResults(null);
                      setSelectedImage(null);
                    }}
                    className="text-gray-400 hover:text-red-400 transition-colors transform hover:scale-110"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <div className="text-xs text-gray-500">
                  总大小: {files.reduce((acc, f) => acc + parseFloat(f.size), 0).toFixed(2)} MB
                </div>
              </div>
            </div>
          )}
        </Card>

        {/* 批量参数 */}
        <Card title="批量参数">
          <div className="space-y-4">
            <SliderControl
              label="并行进程数"
              value={batchParams.numWorkers}
              onChange={(v) => setBatchParams({...batchParams, numWorkers: v})}
              min={1}
              max={16}
              hint="更多进程可以提高处理速度"
            />

            <div className="p-4 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-lg border border-cyan-500/20">
              <h4 className="text-sm font-bold text-cyan-400 mb-3 flex items-center gap-2">
                <Settings className="w-4 h-4" />
                处理参数（与单图像处理相同）
              </h4>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <SliderControl
                    label="采样率"
                    value={batchParams.sampleRatio}
                    onChange={(v) => setBatchParams({...batchParams, sampleRatio: v})}
                    min={1}
                    max={100}
                    unit="%"
                  />
                  
                  <SliderControl
                    label="输出分辨率"
                    value={batchParams.resolution}
                    onChange={(v) => setBatchParams({...batchParams, resolution: v})}
                    min={50}
                    max={200}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-bold text-gray-200 mb-2 block">圆柱拟合方法</label>
                    <select
                      value={batchParams.method}
                      onChange={(e) => setBatchParams({...batchParams, method: e.target.value})}
                      className="w-full bg-gray-700 rounded-lg px-3 py-2 text-gray-200 text-sm"
                    >
                      <option value="pca">PCA主成分分析</option>
                      <option value="ransac">RANSAC随机采样</option>
                    </select>
                  </div>

                  <SliderControl
                    label="偏差阈值"
                    value={batchParams.deviationThreshold}
                    onChange={(v) => setBatchParams({...batchParams, deviationThreshold: v})}
                    min={0.1}
                    max={1.0}
                    step={0.1}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <SliderControl
                    label="角度分区数"
                    value={batchParams.nBins}
                    onChange={(v) => setBatchParams({...batchParams, nBins: v})}
                    min={36}
                    max={144}
                  />

                  <SliderControl
                    label="滑动窗口"
                    value={batchParams.windowSize}
                    onChange={(v) => setBatchParams({...batchParams, windowSize: v})}
                    min={6}
                    max={24}
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
              <label className="text-sm font-bold text-gray-200">跳过已存在文件</label>
              <button
                onClick={() => setBatchParams({...batchParams, skipExisting: !batchParams.skipExisting})}
                className={`relative w-12 h-6 rounded-full transition-all duration-300 ${
                  batchParams.skipExisting ? 'bg-gradient-to-r from-cyan-500 to-blue-500' : 'bg-gray-600'
                }`}
              >
                <div className={`absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform shadow-lg ${
                  batchParams.skipExisting ? 'translate-x-6' : ''
                }`} />
              </button>
            </div>

            <button
              onClick={handleBatchProcess}
              disabled={files.length === 0 || isProcessing}
              className={`w-full py-3 rounded-lg font-bold transition-all flex items-center justify-center gap-2 shadow-lg ${
                files.length === 0 || isProcessing
                  ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white hover:from-cyan-600 hover:to-blue-600 hover:shadow-cyan-500/25 transform hover:scale-105'
              }`}
            >
              <Play className="w-5 h-5" />
              开始批量处理
            </button>
          </div>
        </Card>
      </div>

      {/* 批量处理进度 */}
      {(isProcessing || files.some(f => f.status !== 'waiting')) && (
        <Card title="批量处理进度">
          <div className="space-y-4">
            {/* 整体进度条 */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-sm font-semibold text-gray-300">整体进度</h4>
                <span className="text-sm text-gray-400">
                  预计剩余时间：<span className="text-cyan-400 font-semibold">{estimatedTime}秒</span>
                </span>
              </div>
              <ProgressBar percentage={totalProgress} />
              {completedCount > 0 && (
                <p className="text-xs text-gray-500 mt-2">
                  提示：点击已完成文件的"查看结果"按钮可查看处理结果
                </p>
              )}
            </div>
            
            {/* 统计信息 */}
            <div className="grid grid-cols-4 gap-4">
              <StatCard icon={Package} label="总文件数" value={files.length} color="cyan" />
              <StatCard icon={CheckCircle} label="已完成" value={completedCount} color="green" />
              <StatCard icon={RefreshCw} label="处理中" value={processingCount} color="purple" />
              <StatCard icon={Zap} label="处理速度" value="260+ MB/s" color="orange" />
            </div>

            {/* 文件列表表格 */}
            {files.length > 0 && (
              <div className="bg-gray-700/20 rounded-lg p-1 max-h-96 overflow-y-auto">
                <table className="w-full">
                  <thead className="sticky top-0 bg-gray-800">
                    <tr className="border-b border-gray-700">
                      <th className="text-left py-3 px-4 text-sm font-semibold text-gray-300">文件名</th>
                      <th className="text-center py-3 px-4 text-sm font-semibold text-gray-300">大小</th>
                      <th className="text-center py-3 px-4 text-sm font-semibold text-gray-300">状态</th>
                      <th className="text-center py-3 px-4 text-sm font-semibold text-gray-300">进度</th>
                      <th className="text-center py-3 px-4 text-sm font-semibold text-gray-300">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {files.map(file => (
                      <tr key={file.id} className="border-b border-gray-700/50 hover:bg-gray-700/20">
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(file.status)}
                            <span className="text-sm text-gray-200">{file.name}</span>
                          </div>
                        </td>
                        <td className="text-center py-3 px-4 text-sm text-gray-400">{file.size} MB</td>
                        <td className="text-center py-3 px-4">
                          <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                            file.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                            file.status === 'processing' ? 'bg-cyan-500/20 text-cyan-400' :
                            file.status === 'failed' ? 'bg-red-500/20 text-red-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {file.status === 'waiting' && '等待中'}
                            {file.status === 'processing' && '处理中'}
                            {file.status === 'completed' && '已完成'}
                            {file.status === 'failed' && '失败'}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="w-24 mx-auto">
                            {file.status === 'processing' ? (
                              <ProgressBar percentage={file.progress} showLabel={false} />
                            ) : (
                              <div className="text-center text-sm text-gray-500">
                                {file.status === 'completed' ? '100%' : '-'}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="text-center py-3 px-4">
                          {file.status === 'completed' && (
                            <button
                              onClick={() => handleSelectFile(file.id)}
                              className={`text-sm px-3 py-1 rounded-lg transition-colors ${
                                selectedFileId === file.id
                                  ? 'bg-cyan-500 text-white'
                                  : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                              }`}
                            >
                              查看结果
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* 处理结果查看 */}
      {selectedFileResults && (
        <Card title={`处理结果 - ${files.find(f => f.id === selectedFileId)?.name}`}>
          <ImageViewer
            images={selectedFileResults.images}
            selectedImage={selectedImage}
            onSelectImage={setSelectedImage}
          />
          
          <div className="mt-4 flex gap-4">
            <button className="flex-1 bg-gray-700 hover:bg-gray-600 text-gray-200 py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
              <Download className="w-4 h-4" />
              下载当前图像
            </button>
            <button className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
              <Download className="w-4 h-4" />
              下载该文件全部结果
            </button>
          </div>
          
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div className="bg-gray-700/30 rounded-lg p-3 text-center">
              <p className="text-sm text-gray-400">处理时间</p>
              <p className="text-xl font-bold text-cyan-400">{selectedFileResults.processTime}秒</p>
            </div>
            <div className="bg-gray-700/30 rounded-lg p-3 text-center">
              <p className="text-sm text-gray-400">处理速度</p>
              <p className="text-xl font-bold text-green-400">{selectedFileResults.processSpeed} MB/s</p>
            </div>
          </div>
        </Card>
      )}

      {/* 下载按钮 */}
      {completedCount > 0 && (
        <Card>
          <button className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white py-3 rounded-lg font-bold transition-all flex items-center justify-center gap-2">
            <Download className="w-5 h-5" />
            下载全部处理结果 ({completedCount} 个文件)
          </button>
        </Card>
      )}
    </div>
  );
};

// 特征提取页面
const FeatureExtraction = () => {
  const [files, setFiles] = useState([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [results, setResults] = useState(null);
  const [sampleRatio, setSampleRatio] = useState(5);
  const [currentProgress, setCurrentProgress] = useState(0);
  const [currentStage, setCurrentStage] = useState('');
  const [processedFiles, setProcessedFiles] = useState(0);
  const [fileProgresses, setFileProgresses] = useState({});

  const handleFilesSelect = (selectedFiles) => {
    setFiles(selectedFiles);
    setResults(null);
    setProcessedFiles(0);
    setCurrentProgress(0);
    setCurrentStage('');
    
    // 初始化每个文件的进度
    const progresses = {};
    selectedFiles.forEach((file, index) => {
      progresses[index] = { progress: 0, status: 'waiting' };
    });
    setFileProgresses(progresses);
  };

  const handleExtract = () => {
    if (files.length === 0) return;
    setIsExtracting(true);
    setCurrentProgress(0);
    setProcessedFiles(0);
    setCurrentStage('初始化...');
    
    const isSingleFile = files.length === 1;
    
    if (isSingleFile) {
      // 单文件处理逻辑
      const stages = [
        { progress: 10, stage: '读取文件中...' },
        { progress: 25, stage: '预处理点云数据...' },
        { progress: 40, stage: '提取空间特征...' },
        { progress: 55, stage: '提取颜色特征...' },
        { progress: 70, stage: '提取密度特征...' },
        { progress: 85, stage: '提取几何特征...' },
        { progress: 95, stage: '提取纹理特征...' },
        { progress: 100, stage: '保存结果...' }
      ];
      
      let stageIndex = 0;
      const interval = setInterval(() => {
        setCurrentProgress(prev => {
          const newProgress = Math.min(prev + 2, 100);
          
          while (stageIndex < stages.length && newProgress >= stages[stageIndex].progress) {
            setCurrentStage(stages[stageIndex].stage);
            stageIndex++;
          }
          
          if (newProgress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
              setResults({
                fileCount: 1,
                features: {
                  spatial: 9,
                  color: 21,
                  density: 15,
                  geometric: 20,
                  texture: 24
                },
                totalFeatures: 89,
                processSpeed: 105.3,
                timeBreakdown: {
                  reading: 2.1,
                  preprocessing: 3.5,
                  spatialFeatures: 1.2,
                  colorFeatures: 2.8,
                  densityFeatures: 4.1,
                  geometricFeatures: 3.9,
                  textureFeatures: 5.2,
                  saving: 0.8
                }
              });
              setIsExtracting(false);
              setCurrentStage('');
            }, 500);
          }
          return newProgress;
        });
      }, 100);
    } else {
      // 批量处理逻辑
      let totalProcessed = 0;
      const totalFiles = files.length;
      
      // 模拟批量处理
      files.forEach((file, index) => {
        setTimeout(() => {
          setFileProgresses(prev => ({
            ...prev,
            [index]: { progress: 0, status: 'processing' }
          }));
          
          const fileInterval = setInterval(() => {
            setFileProgresses(prev => {
              const newProgress = Math.min(prev[index].progress + 10, 100);
              
              if (newProgress >= 100) {
                clearInterval(fileInterval);
                totalProcessed++;
                setProcessedFiles(totalProcessed);
                
                // 更新总进度
                setCurrentProgress((totalProcessed / totalFiles) * 100);
                
                if (totalProcessed === totalFiles) {
                  // 所有文件处理完成
                  setTimeout(() => {
                    setResults({
                      fileCount: totalFiles,
                      features: {
                        spatial: 9,
                        color: 21,
                        density: 15,
                        geometric: 20,
                        texture: 24
                      },
                      totalFeatures: 89,
                      processSpeed: 268.7,
                      timeBreakdown: {
                        reading: 2.1 * totalFiles,
                        preprocessing: 3.5 * totalFiles,
                        spatialFeatures: 1.2 * totalFiles,
                        colorFeatures: 2.8 * totalFiles,
                        densityFeatures: 4.1 * totalFiles,
                        geometricFeatures: 3.9 * totalFiles,
                        textureFeatures: 5.2 * totalFiles,
                        saving: 0.8 * totalFiles
                      }
                    });
                    setIsExtracting(false);
                    setCurrentStage('');
                  }, 500);
                }
                
                return {
                  ...prev,
                  [index]: { progress: 100, status: 'completed' }
                };
              }
              
              return {
                ...prev,
                [index]: { progress: newProgress, status: 'processing' }
              };
            });
          }, 200);
        }, index * 500); // 错开开始时间
      });
      
      setCurrentStage('批量提取特征中...');
    }
  };

  const isSingleFile = files.length === 1;

  return (
    <div className="space-y-6">
      {/* 第一行：文件上传和参数设置 */}
      <div className="grid grid-cols-2 gap-6">
        {/* 文件上传 */}
        <Card title="文件上传" icon={Upload} gradient>
          {files.length === 0 ? (
            <FileDropzone onFilesSelected={handleFilesSelect} multiple acceptFolder />
          ) : (
            <div className="space-y-2">
              {isSingleFile ? (
                <div className="bg-gradient-to-r from-gray-700/30 to-gray-800/30 rounded-xl p-4 flex items-center justify-between group hover:from-gray-700/40 hover:to-gray-800/40 transition-all duration-300">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-cyan-500/25">
                      <FileText className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="font-semibold text-gray-200">{files[0].name}</p>
                      <p className="text-sm text-gray-400">
                        {(files[0].size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setFiles([]);
                      setResults(null);
                      setCurrentProgress(0);
                    }}
                    className="text-gray-400 hover:text-red-400 transition-colors transform hover:scale-110"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              ) : (
                <div className="bg-gradient-to-r from-gray-700/30 to-gray-800/30 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-cyan-500/25">
                        <FolderOpen className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <p className="font-semibold text-gray-200">{files.length} 个文件</p>
                        <p className="text-sm text-gray-400">
                          总大小: {(files.reduce((acc, f) => acc + f.size, 0) / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => {
                        setFiles([]);
                        setResults(null);
                        setCurrentProgress(0);
                      }}
                      className="text-gray-400 hover:text-red-400 transition-colors transform hover:scale-110"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </Card>

        {/* 参数设置 */}
        <Card title="参数设置" icon={Settings} gradient>
          <div className="space-y-6">
            <div className="p-4 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg border border-purple-500/20">
              <SliderControl
                label="采样率"
                value={sampleRatio}
                onChange={setSampleRatio}
                min={1}
                max={100}
                unit="%"
                hint="建议使用5%以平衡速度和精度"
              />
            </div>

            <button
              onClick={handleExtract}
              disabled={files.length === 0 || isExtracting}
              className={`w-full py-3 rounded-xl font-bold transition-all flex items-center justify-center gap-3 shadow-lg ${
                files.length === 0 || isExtracting
                  ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 hover:shadow-purple-500/25 transform hover:scale-105'
              }`}
            >
              {isExtracting ? (
                <>
                  <RefreshCw className="w-5 h-5 animate-spin" />
                  提取中...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5" />
                  开始提取
                </>
              )}
            </button>
          </div>
        </Card>
      </div>

      {/* 提取进度 */}
      {isExtracting && (
        <Card title="特征提取进度">
          <div className="space-y-4">
            {/* 主进度条 */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-semibold text-gray-300">
                  {isSingleFile ? '提取进度' : `批量提取进度 (${processedFiles}/${files.length})`}
                </span>
                <span className="text-sm font-bold text-cyan-400">
                  {currentProgress.toFixed(0)}%
                </span>
              </div>
              <ProgressBar percentage={currentProgress} showLabel={false} />
            </div>
            
            {/* 单文件：特征提取进度细节 */}
            {isSingleFile ? (
              <>
                {/* 当前阶段 */}
                <div className="bg-gray-700/30 rounded-lg p-3">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="w-4 h-4 text-cyan-400 animate-spin" />
                    <p className="text-sm text-gray-300">
                      当前阶段：<span className="text-cyan-400 font-semibold">{currentStage}</span>
                    </p>
                  </div>
                </div>
                
                {/* 特征提取进度细节 */}
                <div className="space-y-2">
                  <p className="text-xs font-semibold text-gray-400 mb-2">特征提取进度细节</p>
                  <div className="grid grid-cols-5 gap-2">
                    {[
                      { name: '空间', progress: currentProgress >= 40 ? 100 : currentProgress * 2.5 },
                      { name: '颜色', progress: currentProgress >= 55 ? 100 : Math.max(0, (currentProgress - 40) * 6.67) },
                      { name: '密度', progress: currentProgress >= 70 ? 100 : Math.max(0, (currentProgress - 55) * 6.67) },
                      { name: '几何', progress: currentProgress >= 85 ? 100 : Math.max(0, (currentProgress - 70) * 6.67) },
                      { name: '纹理', progress: currentProgress >= 100 ? 100 : Math.max(0, (currentProgress - 85) * 6.67) }
                    ].map((feature, index) => (
                      <div key={index} className="text-center">
                        <div className="h-16 bg-gray-700/50 rounded-lg relative overflow-hidden">
                          <div
                            className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-cyan-500/50 to-cyan-500/20 transition-all duration-500"
                            style={{ height: `${feature.progress}%` }}
                          />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-xs font-bold text-gray-300">{feature.name}</span>
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{Math.floor(feature.progress)}%</p>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              /* 批量处理：文件进度列表 */
              <>
                <div className="bg-gray-700/30 rounded-lg p-3">
                  <p className="text-sm text-gray-300">
                    当前状态：<span className="text-cyan-400 font-semibold">{currentStage}</span>
                  </p>
                </div>
                
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {files.map((file, index) => {
                    const fileProgress = fileProgresses[index] || { progress: 0, status: 'waiting' };
                    return (
                      <div key={index} className="bg-gray-700/20 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {fileProgress.status === 'waiting' && <Clock className="w-4 h-4 text-gray-400" />}
                            {fileProgress.status === 'processing' && <RefreshCw className="w-4 h-4 text-cyan-400 animate-spin" />}
                            {fileProgress.status === 'completed' && <CheckCircle className="w-4 h-4 text-green-400" />}
                            <span className="text-sm text-gray-200">{file.name}</span>
                          </div>
                          <span className="text-xs text-gray-400">{fileProgress.progress}%</span>
                        </div>
                        <ProgressBar percentage={fileProgress.progress} showLabel={false} />
                      </div>
                    );
                  })}
                </div>
              </>
            )}
            
            {/* 统计信息 */}
            <div className="grid grid-cols-2 gap-4">
              <StatCard 
                icon={Zap} 
                label="处理速度" 
                value={isSingleFile ? "100+ MB/s" : "260+ MB/s"} 
                color="cyan" 
              />
              <StatCard 
                icon={Timer} 
                label="预计剩余" 
                value={`${Math.ceil((100 - currentProgress) / 10)}秒`} 
                color="green" 
              />
            </div>
          </div>
        </Card>
      )}

      {/* 特征结果 */}
      {results && (
        <>
          <Card title="89维特征提取结果">
            <div className="space-y-6">
              {/* 特征分解 */}
              <div className="bg-gray-700/30 rounded-lg p-4">
                <h4 className="text-lg font-bold text-gray-100 mb-4">
                  特征详细分解 {!isSingleFile && `(${results.fileCount} 个文件)`}
                </h4>
                <FeatureBarChart features={results.features} />
              </div>

              {/* 特征统计 */}
              <div className="grid grid-cols-5 gap-3">
                {Object.entries(results.features).map(([key, value]) => (
                  <div key={key} className="bg-gray-700/30 rounded-lg p-3 text-center">
                    <p className="text-2xl font-bold text-cyan-400">{value}</p>
                    <p className="text-xs text-gray-400">
                      {key === 'spatial' && '空间特征'}
                      {key === 'color' && '颜色特征'}
                      {key === 'density' && '密度特征'}
                      {key === 'geometric' && '几何特征'}
                      {key === 'texture' && '纹理特征'}
                    </p>
                  </div>
                ))}
              </div>

              {/* 时间分解 */}
              <div>
                <h4 className="text-sm font-semibold text-gray-300 mb-3">各阶段时间分解</h4>
                <div className="space-y-2">
                  {Object.entries(results.timeBreakdown).map(([stage, time]) => (
                    <div key={stage} className="flex justify-between text-sm">
                      <span className="text-gray-400">
                        {stage === 'reading' && '文件读取'}
                        {stage === 'preprocessing' && '预处理'}
                        {stage === 'spatialFeatures' && '空间特征'}
                        {stage === 'colorFeatures' && '颜色特征'}
                        {stage === 'densityFeatures' && '密度特征'}
                        {stage === 'geometricFeatures' && '几何特征'}
                        {stage === 'textureFeatures' && '纹理特征'}
                        {stage === 'saving' && '保存结果'}
                      </span>
                      <span className="text-cyan-400 font-mono">{time.toFixed(1)}秒</span>
                    </div>
                  ))}
                  <div className="border-t border-gray-700 pt-2 flex justify-between text-sm font-semibold">
                    <span className="text-gray-300">总计</span>
                    <span className="text-cyan-400 font-mono">
                      {Object.values(results.timeBreakdown).reduce((a, b) => a + b, 0).toFixed(1)}秒
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <button className="flex-1 bg-gray-700 hover:bg-gray-600 text-gray-200 py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
                  <Download className="w-4 h-4" />
                  下载特征文件 (.npy)
                </button>
                <button className="flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2">
                  <FileText className="w-4 h-4" />
                  查看详细报告
                </button>
              </div>
            </div>
          </Card>

          <Card title="处理性能">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                <TrendingUp className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-3xl font-bold text-cyan-400">{results.processSpeed} MB/s</p>
                <p className="text-sm text-gray-400">处理速度</p>
              </div>
              <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                <BarChart3 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-3xl font-bold text-green-400">{results.totalFeatures}维</p>
                <p className="text-sm text-gray-400">特征总数</p>
              </div>
            </div>
          </Card>
        </>
      )}
    </div>
  );
};

// 主应用组件
const App = () => {
  const [currentPage, setCurrentPage] = useState('single');

  const navigation = [
    { id: 'single', name: '单图像隧道展开', component: SingleTunnel },
    { id: 'batch', name: '批量隧道处理', component: BatchProcess },
    { id: 'feature', name: '特征提取分析', component: FeatureExtraction }
  ];

  const CurrentComponent = navigation.find(nav => nav.id === currentPage)?.component || SingleTunnel;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-gray-100 relative overflow-hidden">
      {/* 背景装饰 - 网格和流动线条 */}
      <div className="absolute inset-0">
        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-10">
          <div 
            className="h-full w-full"
            style={{
              backgroundImage: `
                linear-gradient(cyan 1px, transparent 1px),
                linear-gradient(90deg, cyan 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px',
              backgroundPosition: '-1px -1px'
            }}
          />
        </div>
        
        {/* 流动的渐变线条 */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyan-500 to-transparent animate-slide-right" />
          <div className="absolute top-1/4 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-500 to-transparent animate-slide-right animation-delay-1000" />
          <div className="absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-purple-500 to-transparent animate-slide-right animation-delay-2000" />
          <div className="absolute top-3/4 left-0 w-full h-1 bg-gradient-to-r from-transparent via-pink-500 to-transparent animate-slide-right animation-delay-3000" />
        </div>
        
        {/* 垂直流动线条 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute left-1/4 top-0 w-0.5 h-full bg-gradient-to-b from-transparent via-cyan-500 to-transparent animate-slide-down" />
          <div className="absolute left-1/2 top-0 w-0.5 h-full bg-gradient-to-b from-transparent via-blue-500 to-transparent animate-slide-down animation-delay-1500" />
          <div className="absolute left-3/4 top-0 w-0.5 h-full bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-slide-down animation-delay-3000" />
        </div>
        
        {/* 角落装饰 */}
        <div className="absolute top-0 left-0 w-96 h-96">
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-transparent" />
        </div>
        <div className="absolute bottom-0 right-0 w-96 h-96">
          <div className="absolute inset-0 bg-gradient-to-tl from-blue-500/10 via-transparent to-transparent" />
        </div>
      </div>
      
      {/* 顶部导航栏 */}
      <div className="relative bg-gray-900/70 backdrop-blur-md border-b border-gray-700/50">
        <div className="px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-cyan-500/25">
                <Zap className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-black text-gray-100">LAS隧道点云处理系统</h1>
                <p className="text-xs text-gray-400">高性能点云数据处理平台</p>
              </div>
            </div>
            
            <div className="flex gap-2 bg-gray-800/50 backdrop-blur-xl p-1.5 rounded-xl border border-gray-700/50">
              {navigation.map(nav => (
                <button
                  key={nav.id}
                  onClick={() => setCurrentPage(nav.id)}
                  className={`px-6 py-2.5 rounded-lg font-semibold transition-all duration-300 ${
                    currentPage === nav.id
                      ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-lg shadow-cyan-500/25'
                      : 'text-gray-400 hover:text-gray-200 hover:bg-gray-700/50'
                  }`}
                >
                  {nav.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 主内容区 */}
      <div className="relative p-8">
        <CurrentComponent />
      </div>

      {/* 自定义CSS */}
      <style jsx>{`
        /* 滑块样式 */
        .slider-thumb::-webkit-slider-thumb {
          appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: linear-gradient(135deg, #00d4ff 0%, #0066ff 100%);
          cursor: pointer;
          box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
          transition: all 0.2s ease;
        }
        
        .slider-thumb::-webkit-slider-thumb:hover {
          transform: scale(1.2);
          box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }
        
        .slider-thumb::-moz-range-thumb {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: linear-gradient(135deg, #00d4ff 0%, #0066ff 100%);
          border: none;
          cursor: pointer;
          box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
          transition: all 0.2s ease;
        }
        
        .slider-thumb::-moz-range-thumb:hover {
          transform: scale(1.2);
          box-shadow: 0 0 30px rgba(0, 212, 255, 0.8);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
        }
        
        ::-webkit-scrollbar-track {
          background: rgba(31, 41, 55, 0.5);
          border-radius: 5px;
        }
        
        ::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #475569, #334155);
          border-radius: 5px;
          transition: all 0.3s ease;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #64748b, #475569);
        }
        
        /* 动画延迟 */
        .animation-delay-1000 {
          animation-delay: 1s;
        }
        
        .animation-delay-1500 {
          animation-delay: 1.5s;
        }
        
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        
        .animation-delay-3000 {
          animation-delay: 3s;
        }
        
        /* 流动线条动画 */
        @keyframes slide-right {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(200%);
          }
        }
        
        @keyframes slide-down {
          0% {
            transform: translateY(-100%);
          }
          100% {
            transform: translateY(200%);
          }
        }
        
        .animate-slide-right {
          animation: slide-right 10s linear infinite;
        }
        
        .animate-slide-down {
          animation: slide-down 15s linear infinite;
        }
        
        /* 渐变动画 */
        @keyframes gradient {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
      `}</style>
    </div>
  );
};

export default App;