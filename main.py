"""
LAS隧道点云处理系统 - 主入口文件
整合后的统一接口
"""

import os
import sys
from datetime import datetime

# 添加src路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from las_processor.processors import OptimizedFeatureExtractor, batch_process_optimized
from las_processor.core.processors.single_tunnel_processor import SingleTunnelProcessor
from las_processor.core.processors.batch_processor import BatchProcessor

def single_feature_extraction(las_file, output_dir=None):
    """单文件特征提取"""
    if output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = f"results/特征提取_{timestamp}"
    
    os.makedirs(output_dir, exist_ok=True)
    
    config = {
        'sample_ratio': 0.05,
        'use_vectorized_ops': True,
        'optimize_grid_computation': True,
        'enable_all_features': True
    }
    
    extractor = OptimizedFeatureExtractor(config)
    success, features, timings = extractor.extract_features_optimized(las_file, output_dir)
    
    return success, features, timings

def batch_feature_extraction():
    """批量特征提取"""
    return batch_process_optimized()

def single_tunnel_unwrap(las_file, output_dir=None):
    """单图像隧道展开"""
    if output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(os.path.basename(las_file))[0]
        output_dir = f"results/隧道展开_{timestamp}/{base_name}"
    
    os.makedirs(output_dir, exist_ok=True)
    
    config = {
        'preprocessing': {
            'sample_ratio': 0.1,
            'min_points': 10000,
            'max_points': 1000000
        },
        'fitting': {
            'method': 'pca'
        },
        'floor_detection': {
            'n_bins': 72,
            'window_size': 12,
            'save_visualization': True
        },
        'filtering': {
            'deviation_threshold': 0.5,
            'use_sor': False,
            'use_ror': False
        },
        'unwrapping': {
            'resolution': 100,
            'use_gpu': False
        },
        'output': {
            'save_filtered_las': False,
            'save_diagnostics': True,
            'base_output_dir': output_dir
        }
    }
    
    processor = SingleTunnelProcessor(config)
    success, stats = processor.process(las_file, output_dir)
    
    return success, stats

def batch_tunnel_unwrap(input_dir="LAS(0-1000)", num_files=5):
    """批量隧道展开 - 每个文件一个文件夹，里面放5张图，保存到results目录"""

    config = {
        'batch': {
            'num_workers': 3,
            'skip_existing': False,
            'save_summary': True
        },
        'processing': {
            'sample_ratio': 0.2,  # 20%采样，加快处理
            'min_points': 10000,
            'max_points': 500000,
            'deviation_threshold': 0.5,
            'resolution': 50,  # 降低分辨率
            'use_gpu': False
        }
    }

    processor = BatchProcessor(config)

    # 使用process_folder方法，它会为每个文件创建单独的文件夹，并保存到results目录
    results = processor.process_folder(input_dir)

    # 统计结果
    successful = sum(1 for r in results if r.get('success', False))
    total = len(results)

    stats = {
        'total': total,
        'successful': successful,
        'failed': total - successful
    }

    return stats

if __name__ == "__main__":
    print("🚀 LAS隧道点云处理系统 - 整合版本")
    print("可用功能:")
    print("1. single_feature_extraction(las_file) - 单文件特征提取")
    print("2. batch_feature_extraction() - 批量特征提取")
    print("3. single_tunnel_unwrap(las_file) - 单图像隧道展开")
    print("4. batch_tunnel_unwrap() - 批量隧道展开")
