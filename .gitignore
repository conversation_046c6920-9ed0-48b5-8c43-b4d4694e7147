# LAS隧道点云处理系统 - Git忽略文件

# ===== 点云数据文件 =====
# LAS/LAZ点云文件
*.las
*.laz
*.LAZ
*.LAS

# LAS数据目录
LAS*/
las*/
LAS(*)
LAS[*]
*LAS*/
*las*/

# 其他点云格式
*.ply
*.pcd
*.xyz
*.pts
*.asc

# ===== 处理结果和输出文件 =====
# 结果目录
results/
test_results/
output/
outputs/
*_output/
*_results/
*_result/
batch_results/
single_results/
feature_results/

# 临时处理目录
temp/
tmp/
*_temp/
*_tmp/
temp_*/
tmp_*/

# 测试输出
test_*/
*_test/
*test*/

# ===== 生成的图片和媒体文件 =====
# 图片文件
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff
*.tif
*.svg

# 视频文件
*.mp4
*.avi
*.mov
*.wmv

# ===== Python相关 =====
# 字节码缓存
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# ===== 日志文件 =====
# 日志目录和文件
logs/
log/
*.log
*.log.*
*.out

# ===== 配置和密钥文件 =====
# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 配置文件（如果包含敏感信息）
config.ini
config.json
settings.json
secrets.json

# ===== 系统文件 =====
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== IDE和编辑器 =====
# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 数据库文件 =====
*.db
*.sqlite
*.sqlite3

# ===== 压缩文件 =====
*.zip
*.tar.gz
*.rar
*.7z

# ===== 特定于项目的忽略 =====
# 特征文件
*.npy
*.npz
*.h5
*.hdf5

# 模型文件
*.pkl
*.pickle
*.joblib

# 大型数据文件
*.csv
*.xlsx
*.json
data/
datasets/

# 文档生成
docs/_build/
site/

# 备份文件
*.bak
*.backup
*.old
*~

# ===== 保留重要文件的例外 =====
# 确保重要的配置文件不被忽略
!requirements.txt
!README.md
!LICENSE
!.gitignore

# 保留示例配置文件
!config.example.json
!settings.example.ini

# 保留小型测试数据（如果有的话）
!test_data/small_sample.las
